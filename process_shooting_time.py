#!/usr/bin/env python3
"""
照片拍摄时间统计工具

功能：
1. 按日统计照片文件数量
2. 提供交互式删除选项
3. 支持按日期删除照片
4. 支持退出选项(q/esc)

使用方式：
python3 process_shooting_time.py <照片目录>
"""

import os
import argparse
from datetime import datetime
from collections import defaultdict

def get_folder_time(filepath):
    """获取文件夹修改时间"""
    return datetime.fromtimestamp(os.path.getmtime(filepath))

def analyze_shooting_times(directory):
    """分析照片拍摄日期"""
    date_stats = defaultdict(list)
    
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if not os.path.isfile(filepath):
            continue
            
        folder_time = get_folder_time(filepath)
        date_str = folder_time.strftime('%Y-%m-%d')
        date_stats[date_str].append(filepath)
    
    print("\n照片拍摄日期统计:")
    print("=================")
    
    # 显示日期统计
    dates = sorted(date_stats.keys())
    for i, date_str in enumerate(dates):
        print(f"{i+1}) {date_str}: {len(date_stats[date_str])}张照片")
    
    print("=================")
    
    # 交互式操作
    while True:
        choice = input("\n输入要删除的日期编号(或q退出): ").strip().lower()
        
        if choice in ('q', 'esc'):
            break
            
        try:
            idx = int(choice) - 1
            if 0 <= idx < len(dates):
                date_str = dates[idx]
                confirm = input(f"确定要删除{date_str}的所有照片({len(date_stats[date_str])}张)? (y/n): ").strip().lower()
                if confirm == 'y':
                    for filepath in date_stats[date_str]:
                        os.remove(filepath)
                    print(f"已删除{date_str}的所有照片")
                    del date_stats[date_str]
                    dates.pop(idx)
                    print("\n更新后的统计:")
                    for i, date_str in enumerate(dates):
                        print(f"{i+1}) {date_str}: {len(date_stats[date_str])}张照片")
                else:
                    print("取消删除操作")
            else:
                print("无效的编号")
        except ValueError:
            print("无效输入，请输入数字编号或q退出")

if __name__ == '__main__':
    parser = argparse.ArgumentParser(description='照片拍摄时间统计工具')
    parser.add_argument('directory', help='要统计的照片目录')
    args = parser.parse_args()
    
    if not os.path.isdir(args.directory):
        print(f"错误: {args.directory} 不是有效目录")
        exit(1)
        
    analyze_shooting_times(args.directory)