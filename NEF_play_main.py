#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NEF照片处理主程序 v1.2

主要功能：
1. 提供统一交互菜单系统
2. 集成照片处理全流程工具：
   - 拍摄时间处理
   - JPG/NEF文件分离
   - Exif信息添加
   - 自动分类标记
   - 照片浏览与评分
3. 自动化处理流程管理
4. 完整的日志记录系统

使用方法：
1. 运行主程序: python3 NEF_play_main.py
2. 根据菜单选择功能
3. 选择NCZ_9格式目录进行处理
4. 查看日志文件获取详细处理记录

依赖：
- Python 3.6+
- 各功能子模块脚本
- 标准日志模块
"""

import os
import sys
import subprocess
import logging
import time
from path_selector import select_directory

# 脚本名称用于显示
script_name = os.path.splitext(os.path.basename(__file__))[0]

# 功能脚本映射
TOOLS = {
    "0": {"name": "处理拍摄时间", "script": "process_shooting_time.py"},
    "1": {"name": "分离JPG/NEF文件", "script": "separate_jpg_files.py"},
    "2": {"name": "YOLOv8分类", "script": "yolov8_image_classifier.py"},
    "3": {"name": "标记对焦点", "script": "mark_focus_point.py"},
    "4": {"name": "添加拍摄参数", "script": "add_shooting_params.py"},
    "5": {"name": "播放和标记", "script": "playback_ncz.py"}
}

def show_menu():
    """显示主菜单"""
    print("\n=== NEF照片处理工具 ===")
    for key, tool in TOOLS.items():
        print(f"{key}. {tool['name']}")
    print("6. 退出程序")

def run_tool(tool, directory):
    """运行指定工具脚本并记录耗时"""
    try:
        start_time = time.time()
        scripts = tool.get("scripts", [tool["script"]] if "script" in tool else [])
        
        for script in scripts:
            cmd = f"python3 {script} {directory}"
            print(f"执行命令: {cmd}")
            result = subprocess.run(cmd, shell=True)
            if result.returncode != 0:
                raise subprocess.CalledProcessError(result.returncode, cmd)
        
        elapsed = time.time() - start_time
        print(f"处理完成，耗时: {elapsed:.2f}秒")
        hours = int(elapsed // 3600)
        minutes = int((elapsed % 3600) // 60)
        print(f"处理耗时: {hours:02d}:{minutes:02d}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"执行失败: {e}")
        print(f"错误: {script} 执行失败")
        return False

def main():
    """主程序入口"""
    while True:
        show_menu()
        choice = input("\n请选择功能 (0-5): ").strip()
        
        if choice == "6":
            print("退出程序")
            print("用户退出程序")
            sys.exit(0)
            
        if choice in TOOLS:
            # 选择目录
            directory = select_directory()
            if not directory:
                continue
                
            # 执行工具
            tool = TOOLS[choice]
            print(f"\n执行: {tool['name']}")
            print(f"开始处理: {tool['name']} - {directory}")
            
            if run_tool(tool, directory):
                print(f"\n{tool['name']} 处理完成")
            else:
                print(f"\n{tool['name']} 处理失败")
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序中断")
        print("程序被用户中断")
    except Exception as e:
        print(f"程序异常: {str(e)}")
        print(f"发生错误: {str(e)}")