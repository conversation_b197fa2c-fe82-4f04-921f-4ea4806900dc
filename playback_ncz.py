#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NCZ选片播放器 (增强版) - 配置说明：
- 版本: 4.0
- 主要功能: 鸟类图片筛选与标记
- 依次显示目录中的JPG图片
- 自动跳过NEF文件
- 超大字体显示当前播放进度(10.0字号)
- 播放时长设置：
  * bird_eye/bird_clear/bird_flying: 0.4秒
  * 其他类别: 0.2秒
- 检测框显示特性：
  * 文字大小：6
  * 文字背景：灰色
  * 框线宽度：4像素
  * 文字粗细：8
  * 文字位置：检测框上方60像素
  * 不同类别使用不同颜色(bird_eye:红色，其他:绿色)
  * bird_eye识别条件：只要检测到bird_eye即保留，不考虑置信度
- 支持暂停/继续播放功能（空格键）
- 支持断点续播功能：
  - 日志记录系统：
    * 隐藏日志：.playback_log.json（自动记录播放位置）
    * 可视日志：playback_log.json（供用户查看的播放记录）
  - 下次启动自动从上次位置继续播放
  - 记录最后播放时间戳
- 支持图片标记功能：
  - 'y'键：标记当前图片并继续播放
  - 'n'键：取消标记当前图片并继续播放
- 方向键：暂停时可前后浏览图片
- 'q'键：保存当前位置并退出
- 使用训练后的YOLOv8模型进行鸟类识别
"""

import os
import cv2
from ultralytics import YOLO
import time
import sys
import json
import shutil
import datetime
import threading
from queue import Queue
import traceback
import subprocess  # 用于调用exiftool

def get_playback_log(directory):
    """获取播放日志文件路径"""
    # 新版本使用可见文件名
    visible_log = os.path.join(directory, "playback_log.json")
    
    # 检查是否存在旧版隐藏文件
    hidden_log = os.path.join(directory, ".playback_log.json")
    if os.path.exists(hidden_log) and not os.path.exists(visible_log):
        try:
            # 将旧文件重命名为新文件
            os.rename(hidden_log, visible_log)
            print(f"已将隐藏日志文件重命名为: {visible_log}")
        except Exception as e:
            print(f"重命名日志文件时出错: {e}")
            return hidden_log
    
    return visible_log

def get_last_position(directory):
    """获取上次退出位置"""
    # 1. 首先检查源目录的播放日志文件
    log_file = get_playback_log(directory)
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r') as f:
                log = json.load(f)
                if 'last_position' in log:
                    print(f"从播放日志恢复位置: 第{log['last_position']+1}张图片 (时间: {log.get('timestamp', '未知')})")
                    return log['last_position']
                else:
                    print("播放日志格式错误，将从头开始播放")
        except Exception as e:
            print(f"读取播放日志时出错: {e}")
            print(f"错误详情: {traceback.format_exc()}")
    
    # 2. 向后兼容：检查旧的位置文件
    position_file = os.path.join(os.path.expanduser("~"), ".slideshow_position.json")
    if os.path.exists(position_file):
        try:
            with open(position_file, 'r') as f:
                positions = json.load(f)
                if directory in positions:
                    print(f"从旧位置文件恢复: 第{positions[directory]+1}张图片")
                    return positions[directory]
        except Exception as e:
            print(f"读取旧位置文件时出错: {e}")
    
    # 3. 没有找到任何位置记录
    print("未找到播放记录，将从头开始播放")
    return 0

def update_playback_log(directory, position):
    """更新播放日志"""
    log_file = get_playback_log(directory)
    log_data = {
        'last_position': position,
        'timestamp': datetime.datetime.now().isoformat(),
        'directory': directory
    }
    
    try:
        # 确保目录存在
        os.makedirs(directory, exist_ok=True)
        
        # 打印调试信息
        print(f"正在保存播放日志到: {log_file}")
        
        with open(log_file, 'w') as f:
            json.dump(log_data, f, indent=2)
        print(f"已更新播放日志: 第{position+1}张图片")
        print(f"日志内容: {log_data}")
    except Exception as e:
        print(f"保存播放日志时出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")

def save_position(directory, position):
    """保存退出位置"""
    # 保存到新的播放日志文件
    update_playback_log(directory, position)
    
    # 向后兼容：同时保存到旧的位置文件
    position_file = os.path.join(os.path.expanduser("~"), ".slideshow_position.json")
    positions = {}
    if os.path.exists(position_file):
        try:
            with open(position_file, 'r') as f:
                positions = json.load(f)
        except Exception:
            pass
    
    positions[directory] = position
    
    try:
        with open(position_file, 'w') as f:
            json.dump(positions, f)
    except Exception as e:
        print(f"保存旧位置文件时出错: {e}")

def load_marked_images(directory):
    """加载已标记的图片列表"""
    marked_images = set()
    tags_file = os.path.join(directory, "photo_tags.txt")
    if os.path.exists(tags_file):
        try:
            with open(tags_file, 'r') as f:
                for line in f:
                    marked_images.add(line.strip())
            print(f"已加载 {len(marked_images)} 个标记")
        except Exception as e:
            print(f"读取标记文件时出错: {e}")
    return marked_images

def mark_image(directory, filename, marked_images):
    """标记图片（带重试机制）"""
    marked_images.add(filename)
    
    tags_file = os.path.join(directory, "photo_tags.txt")
    
    for attempt in range(3):
        try:
            with open(tags_file, 'w') as f:
                for img in sorted(marked_images):
                    f.write(f"{img}\n")
            print(f"已标记图片: {filename}")
            return True
        except Exception as e:
            print(f"保存标记时出错 ({attempt+1}/3): {e}")
            time.sleep(0.5)
            if attempt == 2:
                traceback.print_exc()
    return False

def unmark_image(directory, filename, marked_images):
    """取消标记图片"""
    if filename in marked_images:
        marked_images.remove(filename)
        
        tags_file = os.path.join(directory, "photo_tags.txt")
        try:
            with open(tags_file, 'w') as f:
                for img in sorted(marked_images):
                    f.write(f"{img}\n")
            print(f"已取消标记图片: {filename}")
            return True
        except Exception as e:
            print(f"保存标记时出错: {e}")
    return False


def save_marked_files(directory, marked_images):
    """保存标记的NEF+JPG对到桌面"""
    if not marked_images:
        print("没有标记的图片，跳过保存步骤")
        return None
    
    print("\n开始保存标记的NEF+JPG对...")
    
    # 获取源目录名称（不含路径）
    source_dir_name = os.path.basename(directory)
    
    # 创建桌面上的筛选结果目录
    desktop_path = os.path.expanduser("~/Desktop")
    results_dir = os.path.join(desktop_path, "筛选结果")
    os.makedirs(results_dir, exist_ok=True)
    
    # 创建保存目标目录（在筛选结果目录内）
    saved_target_dir = os.path.join(results_dir, f"{source_dir_name}_保存")
    os.makedirs(saved_target_dir, exist_ok=True)
    
    # 复制标记的文件
    copy_count = 0
    nef_count = 0
    
    # 创建文件名映射字典（主文件名 -> 扩展名集合）
    file_map = {}
    for filename in os.listdir(directory):
        base, ext = os.path.splitext(filename)
        ext = ext.lower()
        if ext in ('.nef', '.jpg', '.jpeg'):
            file_map.setdefault(base, set()).add(ext)
    
    # 复制标记的JPG文件及其对应的NEF文件
    for jpg_filename in marked_images:
        # 跳过非JPG文件
        if not jpg_filename.lower().endswith(('.jpg', '.jpeg')):
            continue
        
        # 获取基本文件名（不含扩展名）
        base = os.path.splitext(jpg_filename)[0]
        
        # 复制JPG文件
        src_jpg_path = os.path.join(directory, jpg_filename)
        dst_jpg_path = os.path.join(saved_target_dir, jpg_filename)
        
        if os.path.exists(src_jpg_path):
            shutil.copy2(src_jpg_path, dst_jpg_path)
            copy_count += 1
            print(f"已复制JPG: {jpg_filename}")
        
        # 检查并复制对应的NEF文件
        nef_filename = f"{base}.NEF"
        src_nef_path = os.path.join(directory, nef_filename)
        
        if os.path.exists(src_nef_path):
            dst_nef_path = os.path.join(saved_target_dir, nef_filename)
            shutil.copy2(src_nef_path, dst_nef_path)
            nef_count += 1
            print(f"已复制NEF: {nef_filename}")
    
    print(f"\n保存完成！")
    print(f"已复制 {copy_count} 个标记的JPG文件")
    print(f"已复制 {nef_count} 个对应的NEF文件")
    print(f"文件已保存到: {saved_target_dir}")
    
    return saved_target_dir

def slideshow(directory):
    # 加载YOLOv8模型
    model = YOLO('/Users/<USER>/Desktop/YOLOv8-11/YOLOv8/best.pt')
    # 检测框线宽设置
    detection_line_width = 2  # 设置检测框线宽(统一为2px)
    """播放目录中的JPG图片，单次循环，支持记录位置和标记"""
    # 获取目录中所有JPG文件
    image_files = []
    for filename in os.listdir(directory):
        ext = os.path.splitext(filename)[1].lower()
        if ext in ['.jpg', '.jpeg']:
            image_files.append(os.path.join(directory, filename))
    
    if not image_files:
        print(f"错误: 目录 '{directory}' 中没有JPG图片!")
        return
    
    # 排序文件名
    image_files.sort()
    
    # 加载已标记的图片
    marked_images = load_marked_images(directory)
    
    # 获取上次退出位置
    current_index = get_last_position(directory)
    if current_index > 0 and current_index < len(image_files):
        print(f"从上次退出位置继续播放: 第{current_index+1}张图片")
    else:
        current_index = 0
        print("从头开始播放")
    
    # 创建窗口
    window_name = "NCZ Photo Viewer (Space:Pause/Play  +/-:Speed  y/n:Mark/Unmark  q:Quit)"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    
    print(f"开始播放 {len(image_files)} 张图片")
    print("按空格键暂停/继续，按y���标记并继续播放，按n键取消标记，按q键退出")
    print("暂停时可使用左右方向键浏览前后图片")
    
    # 自动播放标志
    auto_play = True
    last_change_time = time.time()
    display_time = 0.2  # 默认0.2秒
    
    # 设置字体参数 - 使用更大的字体
    font_face = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 10.0  # 超大字体大小(10.0)
    font_thickness = 8  # 增加字体粗细(8)
    text_color_info = (0, 255, 0)  # 绿色
    text_color_mark = (0, 0, 255)  # 红色
    text_color_pause = (0, 0, 255)  # 红色
    
    # 定义Mac键盘上的特殊键码
    MAC_LEFT_ARROW = 2
    MAC_RIGHT_ARROW = 3
    # 添加更多可能的键码
    LEFT_KEYS = [MAC_LEFT_ARROW, 81, 63234, 2424832, 65361, ord('a'), ord('A')]
    RIGHT_KEYS = [MAC_RIGHT_ARROW, 83, 63235, 2555904, 65363, ord('d'), ord('D')]
    
    # 记录上一次显示的图片索引，用于避免重复输出
    last_displayed_index = -1
    
    try:
        while current_index < len(image_files):
            # 读取当前图片和对焦点信息
            img_path = image_files[current_index]
            filename = os.path.basename(img_path)
            
            # 尝试读取对应的NEF文件获取对焦点
            focus_points = []
            nef_path = os.path.join(directory, os.path.splitext(filename)[0] + '.NEF')
            if os.path.exists(nef_path):
                print(f"找到NEF文件: {nef_path}")
            else:
                print(f"未找到对应的NEF文件: {nef_path}")
                try:
                    # 检查exiftool是否可用
                    exiftool_check = subprocess.run(['which', 'exiftool'], capture_output=True)
                    if exiftool_check.returncode != 0:
                        print("错误: exiftool未安装或不在PATH中")
                    else:
                        # 获取AF区域坐标
                        result = subprocess.run(['exiftool', '-AFAreaXPosition', '-AFAreaYPosition',
                                              '-AFAreaWidth', '-AFAreaHeight', '-AFImageWidth', '-AFImageHeight', '-b', nef_path],
                                              capture_output=True, text=True)
                        print(f"exiftool AF区域查询结果: {result.stdout.strip()}")
                        if result.returncode == 0 and result.stdout.strip():
                            # 解析AF区域坐标 (格式: "x y width height af_w af_h")
                            coords = result.stdout.strip().split()
                            if len(coords) >= 4:
                                x, y, w, h = map(int, coords[:4])
                                # 获取AF参考尺寸(如果有)
                                af_w = int(coords[4]) if len(coords) > 4 else 8256
                                af_h = int(coords[5]) if len(coords) > 5 else 5504
                                
                                # 转换为图片坐标
                                img = cv2.imread(img_path)
                                if img is not None:
                                    img_h, img_w = img.shape[:2]
                                    # 计算中心点坐标
                                    center_x = x + w//2
                                    center_y = y + h//2
                                    # 转换为图片实际坐标
                                    scale_x = img_w / af_w
                                    scale_y = img_h / af_h
                                    center_x = int(center_x * scale_x)
                                    center_y = int(center_y * scale_y)
                                    focus_points = [(center_x, center_y)]
                                    print(f"检测到对焦点区域: 中心({center_x}, {center_y}) 尺寸({w}x{h}) 参考尺寸({af_w}x{af_h})")
                                    print(f"JPG图片尺寸: {img_w}x{img_h}")
                                    print(f"坐标转换比例: X={scale_x:.4f} Y={scale_y:.4f}")
                                    print(f"计算过程: AF中心({x+w//2},{y+h//2}) -> 图片中心({center_x},{center_y})")
                except Exception as e:
                    print(f"读取NEF对焦点信息失败: {e}")
            
            # 只有当索引变化时才输出播放信息，避免重复
            if current_index != last_displayed_index:
                print(f"正在播放: [{current_index+1}/{len(image_files)}] {filename}")
                last_displayed_index = current_index
            
            # 多线程预加载机制
            class Preloader(threading.Thread):
                def __init__(self):
                    super().__init__(daemon=True)
                    self.queue = Queue()
                    self.preloaded = {}
                    self.lock = threading.Lock()

                def run(self):
                    while True:
                        index = self.queue.get()
                        if index >= len(image_files):
                            continue
                        try:
                            img = cv2.imread(image_files[index])
                            if img is not None:
                                with self.lock:
                                    self.preloaded[index] = img
                        except Exception as e:
                            print(f"预加载失败: {image_files[index]}")
                        self.queue.task_done()

            if not hasattr(slideshow, 'preloader'):
                slideshow.preloader = Preloader()
                slideshow.preloader.start()

            # 提交预加载任务
            for i in range(current_index + 1, min(current_index + 6, len(image_files))):
                if i not in slideshow.preloader.preloaded:
                    slideshow.preloader.queue.put(i)

            # 获取当前图片
            img = None
            retry_count = 0
            while img is None and retry_count < 3:
                try:
                    with slideshow.preloader.lock:
                        img = slideshow.preloader.preloaded.pop(current_index, None)
                    if img is None:
                        img = cv2.imread(image_files[current_index])
                    retry_count += 1
                except Exception as e:
                    print(f"图片加载失败 ({retry_count}/3): {image_files[current_index]}")
                    time.sleep(0.5)
                    retry_count += 1
            
            if img is None:
                print(f"无法读取图片: {img_path}")
                current_index += 1
                continue
            
            # 调整窗口大小以适应图片
            h, w = img.shape[:2]
            screen_w, screen_h = 1280, 720  # 默认屏幕尺寸
            scale = min(screen_w / w, screen_h / h) * 1.15  # 改为115%缩放
            new_w, new_h = int(w * scale), int(h * scale)
            cv2.resizeWindow(window_name, new_w, new_h)
            
            # 使用YOLOv8进行鸟类识别
            bird_results = model.predict(img)
            
            # 根据检测结果设置播放时长
            display_time = 0.2  # 默认0.2秒
            if len(bird_results) > 0 and hasattr(bird_results[0], 'boxes'):
                for box in bird_results[0].boxes:
                    cls_id = int(box.cls)
                    if cls_id in [2, 3, 4]:  # bird_clear(2), bird_eye(3), bird_flying(4)
                        display_time = 0.4
                        break
            
            # 绘制检测结果
            if len(bird_results) > 0 and hasattr(bird_results[0], 'boxes'):
                # 绘制原始图像
                img_with_text = bird_results[0].orig_img.copy()
                
                # 优先检查是否有bird_eye
                has_bird_eye = any(int(box.cls) == 3 for box in bird_results[0].boxes)
                
                # 绘制检测框
                show_label = True  # 只显示第一个标签
                for box in bird_results[0].boxes:
                    # 如果有bird_eye且当前不是bird_eye，则跳过
                    if has_bird_eye and int(box.cls) != 3:
                        continue
                    xyxy = box.xyxy[0].cpu().numpy()
                    cls_id = int(box.cls)
                    conf = 1.0  # 强制置信度为1.0，忽略原始置信度
                    cls_name = ["bird_back", "bird_blurry", "bird_clear",
                               "bird_eye", "bird_flying", "flying_back",
                               "flying_blurry", "no_bird"][cls_id]
                    
                    # bird_eye用红色，其他用绿色
                    color = (0, 0, 255) if cls_name == "bird_eye" else (0, 255, 0)
                    
                    # 对于bird_eye类别，强制显示100%置信度
                    if cls_name == "bird_eye":
                        conf = 1.0
                    
                    # 绘制检测框
                    cv2.rectangle(img_with_text,
                                (int(xyxy[0]), int(xyxy[1])),
                                (int(xyxy[2]), int(xyxy[3])),
                                color, detection_line_width)  # 识别框线宽使用统一设置)
                    
                    # 只绘制第一个检测框的标签
                    if show_label:
                        # 绘制标签和引线
                        label = f"{cls_name} {conf:.2f}"
                        text_pos = (int(xyxy[0]), int(xyxy[1])-260)  # 上移200像素
                        # 添加文字背景
                        (text_width, text_height), _ = cv2.getTextSize(label, font_face, 5, font_thickness)
                        cv2.rectangle(img_with_text,
                                    (text_pos[0], text_pos[1] - text_height - 9),
                                    (text_pos[0] + text_width, text_pos[1] + 13),
                                    (35,34,34), -1)  # 识别框背景(35,34,34)
                        # 绘制文字
                        cv2.putText(img_with_text, label,
                                   text_pos,
                                   font_face, 5,
                                   (255,255,255), 8)  # 白色文字，粗细为8
                        show_label = False  # 后续检测框不显示标签
                
                # 检查是否有有效检测结果
                if len(bird_results[0].boxes) == 0:
                    cv2.putText(img_with_text, "No birds detected",
                              (50, 150), font_face, font_scale, (0,0,255), font_thickness)
            else:
                img_with_text = img.copy()
                cv2.putText(img_with_text, "No birds detected",
                          (50, 150), font_face, font_scale, (0,0,255), font_thickness)
            
            # 添加对焦点标记和信息
            for (x,y) in focus_points:
                # 绘制对焦点标记
                cv2.circle(img_with_text, (x,y), 15, (255,0,255), 2)  # 紫色圆圈
                cv2.line(img_with_text, (x-20,y), (x+20,y), (255,0,255), 2)  # 横线
                cv2.line(img_with_text, (x,y-20), (x,y+20), (255,0,255), 2)  # 竖线
                
                # 添加对焦点文字说明
                cv2.putText(img_with_text, "Focus Point",
                           (x+25, y+5), font_face, font_scale*0.6, (255,0,255), font_thickness//2)
            
            # 添加文件名和播放信息
            
            # 计算文本大小以便更好地放置
            text_info = f"File {current_index+1}/{len(image_files)}: {filename}"
            (text_width, text_height), _ = cv2.getTextSize(text_info, font_face, font_scale, font_thickness)
            
            # 添加半透明背景
            text_y = img_with_text.shape[0] - 30
            cv2.rectangle(img_with_text,
                        (10, text_y - text_height - 10),
                        (20 + text_width, text_y + 10),
                        (0,0,0), -1)
            cv2.rectangle(img_with_text,
                        (10, text_y - text_height - 10),
                        (20 + text_width, text_y + 10),
                        (255,255,255), 1)
            
            # 显示标记图标
            if filename in marked_images:
                marker_size = int(h * 0.03)
                marker_pos = (w - marker_size - 20, h - marker_size - 20)
                cv2.circle(img_with_text, marker_pos, marker_size, (0, 0, 255), -1)
            
            # 显示帮助提示
            help_text = f'Space:Pause/Play | +/-:Speed ({display_time:.1f}s) | Y/N:Mark/Unmark | Q:Quit'
            (help_width, help_height), _ = cv2.getTextSize(help_text, font_face, font_scale*0.6, font_thickness)
            cv2.putText(img_with_text, help_text,
                       (40, h - 50), font_face, font_scale*0.6, (255,255,255), font_thickness)
            # 显示图片信息（编号和文件名）- 超大显示
            (text_width, text_height), _ = cv2.getTextSize(text_info, font_face, font_scale, font_thickness)
            cv2.rectangle(img_with_text,
                        (40, 30),
                        (70 + text_width, 30 + text_height + 30),
                        (0,0,0), -1)
            cv2.putText(img_with_text, text_info,
                       (50, 30 + text_height + 10), font_face, font_scale, text_color_info, font_thickness)
            
            # 检查是否已标记
            if filename in marked_images:
                mark_text = "Marked"
                (mark_width, mark_height), _ = cv2.getTextSize(mark_text, font_face, font_scale, font_thickness)
                cv2.putText(img_with_text, mark_text,
                           (70, 240 + text_height + 30), font_face, font_scale, text_color_mark, font_thickness)
            
            # 显示暂停状态
            if not auto_play:
                pause_text = "Paused"
                (pause_width, pause_height), _ = cv2.getTextSize(pause_text, font_face, font_scale, font_thickness)
                cv2.putText(img_with_text, pause_text, 
                           (w - pause_width - 20, 60), font_face, font_scale, text_color_pause, font_thickness)
            
            cv2.imshow(window_name, img_with_text)
            
            # 处理按键和自动播放
            if auto_play:
                # 检查是否有鸟
                has_bird = len(bird_results) > 0 and len(bird_results[0].boxes) > 0
                
                # 设置播放时长
                display_time = 0.4 if has_bird else 0.2  # 有鸟0.4秒，无鸟0.2秒
                    
                # 自动播放模式：等待按键，但最多等待display_time秒
                key = cv2.waitKey(1)
                current_time = time.time()
                
                # 如果按下按键或者已经显示足够长时间
                if key != -1 or (current_time - last_change_time) >= display_time:
                    if key == -1:  # 没有按键，但时间到了
                        current_index += 1
                        last_change_time = current_time
                    elif key == ord('q'):  # q键退出
                        save_position(directory, current_index)
                        break
                    elif key == ord('y'):  # y键标记图片并继续
                        if filename not in marked_images:
                            mark_image(directory, filename, marked_images)
                        current_index += 1
                        last_change_time = current_time
                    elif key == ord('n'):  # n键取消标记并继续
                        if filename in marked_images:
                            unmark_image(directory, filename, marked_images)
                        current_index += 1
                        last_change_time = current_time
                    elif key == ord(' '):  # 空格键暂停
                        auto_play = False
                        print(f"已暂停在图片: {filename}")
            else:
                # 暂停模式：等待按键
                key = cv2.waitKey(0)
                if key == ord('q'):  # q键退出
                    save_position(directory, current_index)
                    break
                elif key == ord('y'):  # y键标记图片并继续
                    if filename not in marked_images:
                        mark_image(directory, filename, marked_images)
                    current_index += 1
                    auto_play = True  # 继续自动播放
                    last_change_time = time.time()
                    print("继续自动播放")
                elif key == ord('n'):  # n键取消标记并继续
                    if filename in marked_images:
                        unmark_image(directory, filename, marked_images)
                    current_index += 1
                    auto_play = True  # 继续自动播放
                    last_change_time = time.time()
                    print("继续自动播放")
                elif key == ord(' '):  # 空格键继续
                    auto_play = True
                    last_change_time = time.time()
                    print("继续自动播放")
                elif key in LEFT_KEYS:  # 左方向键或A键
                    if current_index > 0:
                        current_index -= 1
                        print(f"后退到图片: {os.path.basename(image_files[current_index])} ({current_index+1}/{len(image_files)})")
                elif key in RIGHT_KEYS:  # 右方向键或D键
                    if current_index < len(image_files) - 1:
                        current_index += 1
                        print(f"前进到图片: {os.path.basename(image_files[current_index])} ({current_index+1}/{len(image_files)})")
    except KeyboardInterrupt:
        # 捕获Ctrl+C中断
        print("\n检测到键盘中断，保存当前位置并退出")
        save_position(directory, current_index)
    except Exception as e:
        # 捕获其他异常
        print(f"\n程序异常: {e}")
        print("保存当前位置并退出")
        save_position(directory, current_index)
    finally:
        # 无论如何都关闭窗口
        cv2.destroyAllWindows()
        
        # 保存标记的文件
        if marked_images:
            save_marked_files(directory, marked_images)
        
        # 如果正常播放完毕，重置位置
        if current_index >= len(image_files):
            save_position(directory, 0)
            print("播放完毕，下次将从头开始")
        else:
            print("播放中断，下次将从当前位置继续")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        print("请选择输入方式:")
        print("1. 输入三位数字代码")
        print("2. 直接输入完整路径")
        choice = input("请选择(1或2): ")
        
        if choice == "1":
            code = input("请输入三位数字目录代码: ")
            directory = f"/Users/<USER>/Desktop/{code}NCZ_9"
        elif choice == "2":
            directory = input("请输入完整路径: ")
        else:
            print("无效输入，使用默认路径")
            directory = "/Users/<USER>/Desktop/000NCZ_9"
    
    if not os.path.exists(directory):
        print(f"错误: 目录 '{directory}' 不存在!")
    else:
        print(f"开始播放目录: {directory} 中的图片")
        slideshow(directory)

