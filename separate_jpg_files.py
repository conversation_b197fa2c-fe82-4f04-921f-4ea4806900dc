#!/usr/bin/env python3
"""
JPG文件分离工具

功能：
1. 将指定目录中的单JPG文件移动到目标目录
2. 保留NEF+JPG文件对在原目录
3. 在目标目录创建"源目录名_JPG"子目录
"""

import os
import shutil
import time
import logging
from pathlib import Path

# 设置日志
script_name = os.path.splitext(os.path.basename(__file__))[0]
log_file = os.path.abspath(f"{script_name}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(script_name)

start_time = time.time()

def separate_jpg_files(source_dir, target_base_dir):
    """分离单JPG文件"""
    # 创建目标目录
    source_dir_name = os.path.basename(source_dir)
    target_dir = os.path.join(target_base_dir, f"{source_dir_name}_JPG")
    os.makedirs(target_dir, exist_ok=True)

    # 收集所有文件
    files = [f for f in os.listdir(source_dir) if os.path.isfile(os.path.join(source_dir, f))]
    
    # 分离文件
    jpg_files = set()
    nef_files = set()
    
    for file in files:
        name, ext = os.path.splitext(file)
        ext = ext.lower()
        if ext == '.jpg':
            jpg_files.add(name)
        elif ext == '.nef':
            nef_files.add(name)

    # 计算要移动的单JPG文件
    single_jpgs = jpg_files - nef_files
    
    # 移动文件
    moved_count = 0
    for name in single_jpgs:
        src = os.path.join(source_dir, f"{name}.jpg")
        dst = os.path.join(target_dir, f"{name}.jpg")
        shutil.move(src, dst)
        moved_count += 1
    
    elapsed_time = time.time() - start_time
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)
    logger.info(f"已移动{moved_count}个JPG文件到: {target_dir}")
    print(f"已移动{moved_count}个JPG文件到: {target_dir}")
    print(f"总耗时: {hours:02d}:{minutes:02d}")

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='分离单JPG文件工具')
    parser.add_argument('source_dir', help='源照片目录')
    parser.add_argument('--target', default='/Users/<USER>/Desktop/筛选结果', 
                       help='目标基础目录(默认: /Users/<USER>/Desktop/筛选结果)')
    args = parser.parse_args()

    if not os.path.isdir(args.source_dir):
        print(f"错误: {args.source_dir} 不是有效目录")
        exit(1)

    separate_jpg_files(args.source_dir, args.target)