#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLOv8图片查看标记器
- 版本: 1.0
- 主要功能: 使用OpenCV播放指定目录下的JPG文件，并支持YOLOv8检测和标记
- 支持大小写JPG扩展名
- 自动播放功能，可暂停/继续
- YOLOv8目标检测和可视化
- 图片标记功能
- 播放进度显示
"""

import os
import cv2
import time
import json
import datetime
import argparse
from ultralytics import YOLO

def load_marked_images(directory):
    """加载已标记的图片列表"""
    marked_images = set()
    tags_file = os.path.join(directory, "photo_tags.txt")
    if os.path.exists(tags_file):
        try:
            with open(tags_file, 'r') as f:
                for line in f:
                    marked_images.add(line.strip())
            print(f"已加载 {len(marked_images)} 个标记")
        except Exception as e:
            print(f"读取标记文件时出错: {e}")
    return marked_images

def mark_image(directory, filename, marked_images):
    """标记图片"""
    marked_images.add(filename)
    
    tags_file = os.path.join(directory, "photo_tags.txt")
    
    try:
        with open(tags_file, 'w') as f:
            for img in sorted(marked_images):
                f.write(f"{img}\n")
        print(f"已标记图片: {filename}")
        return True
    except Exception as e:
        print(f"保存标记时出错: {e}")
        return False

def unmark_image(directory, filename, marked_images):
    """取消标记图片"""
    if filename in marked_images:
        marked_images.remove(filename)
        
        tags_file = os.path.join(directory, "photo_tags.txt")
        try:
            with open(tags_file, 'w') as f:
                for img in sorted(marked_images):
                    f.write(f"{img}\n")
            print(f"已取消标记图片: {filename}")
            return True
        except Exception as e:
            print(f"保存标记时出错: {e}")
    return False

def view_images(directory, model_path=None):
    """播放目录中的JPG图片，支持YOLOv8检测和标记"""
    
    # 加载YOLOv8模型（如果提供）
    model = None
    if model_path and os.path.exists(model_path):
        try:
            model = YOLO(model_path)
            print(f"已加载YOLOv8模型: {model_path}")
        except Exception as e:
            print(f"加载YOLOv8模型失败: {e}")
            model = None
    else:
        print("未提供模型路径或模型文件不存在，将不进行目标检测")
    
    # 获取目录中所有JPG文件
    image_files = []
    for filename in os.listdir(directory):
        ext = os.path.splitext(filename)[1].lower()
        if ext in ['.jpg', '.jpeg']:
            image_files.append(os.path.join(directory, filename))
    
    if not image_files:
        print(f"错误: 目录 '{directory}' 中没有JPG图片!")
        return
    
    # 排序文件名
    image_files.sort()
    
    # 加载已标记的图片
    marked_images = load_marked_images(directory)
    
    # 创建窗口
    window_name = "YOLOv8 Image Viewer (Space:Pause/Play  y/n:Mark/Unmark  Left/Right:Navigate  q:Quit)"
    cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
    
    print(f"开始播放 {len(image_files)} 张图片")
    print("控制说明:")
    print("  空格键: 暂停/继续播放")
    print("  y键: 标记当前图片")
    print("  n键: 取消标记当前图片")
    print("  左右方向键: 浏览前后图片")
    print("  q键: 退出")
    
    # 播放参数
    current_index = 0
    auto_play = True
    last_change_time = time.time()
    display_time = 0.5  # 默认0.5秒
    
    # 字体参数
    font_face = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 2.0
    font_thickness = 3
    text_color_info = (0, 255, 0)  # 绿色
    text_color_mark = (0, 0, 255)  # 红色
    
    # 检测框参数
    detection_line_width = 3
    
    try:
        while current_index < len(image_files):
            # 读取当前图片
            img_path = image_files[current_index]
            filename = os.path.basename(img_path)
            
            print(f"正在显示: [{current_index+1}/{len(image_files)}] {filename}")
            
            # 读取图片
            img = cv2.imread(img_path)
            if img is None:
                print(f"无法读取图片: {img_path}")
                current_index += 1
                continue
            
            # 调整窗口大小
            h, w = img.shape[:2]
            screen_w, screen_h = 1280, 720
            scale = min(screen_w / w, screen_h / h) * 0.8
            new_w, new_h = int(w * scale), int(h * scale)
            cv2.resizeWindow(window_name, new_w, new_h)
            
            # 复制图片用于绘制
            display_img = img.copy()
            
            # 使用YOLOv8进行检测（如果模型可用）
            if model is not None:
                try:
                    results = model.predict(img, verbose=False)
                    
                    # 绘制检测结果
                    if len(results) > 0 and hasattr(results[0], 'boxes') and results[0].boxes is not None:
                        for box in results[0].boxes:
                            # 获取边界框坐标
                            xyxy = box.xyxy[0].cpu().numpy()
                            cls_id = int(box.cls)
                            conf = float(box.conf)
                            
                            # 获取类别名称
                            cls_name = model.names[cls_id] if cls_id < len(model.names) else f"class_{cls_id}"
                            
                            # 设置颜色（根据类别或置信度）
                            if "bird_eye" in cls_name:
                                color = (0, 0, 255)  # 红色
                            else:
                                color = (0, 255, 0)  # 绿色
                            
                            # 绘制检测框
                            cv2.rectangle(display_img,
                                        (int(xyxy[0]), int(xyxy[1])),
                                        (int(xyxy[2]), int(xyxy[3])),
                                        color, detection_line_width)
                            
                            # 绘制标签
                            label = f"{cls_name} {conf:.2f}"
                            text_pos = (int(xyxy[0]), int(xyxy[1]) - 10)
                            
                            # 添加文字背景
                            (text_width, text_height), _ = cv2.getTextSize(label, font_face, 1.0, 2)
                            cv2.rectangle(display_img,
                                        (text_pos[0], text_pos[1] - text_height - 5),
                                        (text_pos[0] + text_width, text_pos[1] + 5),
                                        (0, 0, 0), -1)
                            
                            # 绘制文字
                            cv2.putText(display_img, label, text_pos, font_face, 1.0, (255, 255, 255), 2)
                            
                except Exception as e:
                    print(f"YOLOv8检测失败: {e}")
            
            # 添加播放信息文字
            info_text = f"[{current_index+1}/{len(image_files)}] {filename}"
            if filename in marked_images:
                info_text += " [MARKED]"
                text_color = text_color_mark
            else:
                text_color = text_color_info
            
            # 在图片上显示信息
            cv2.putText(display_img, info_text, (30, 50), font_face, font_scale, text_color, font_thickness)
            
            # 显示暂停状态
            if not auto_play:
                cv2.putText(display_img, "PAUSED", (30, 120), font_face, font_scale, text_color_mark, font_thickness)
            
            # 显示图片
            cv2.imshow(window_name, display_img)
            
            # 处理键盘输入
            key = cv2.waitKey(50) & 0xFF
            
            if key == ord('q') or key == 27:  # q键或ESC键退出
                break
            elif key == ord(' '):  # 空格键暂停/继续
                auto_play = not auto_play
                if auto_play:
                    last_change_time = time.time()
                print(f"播放状态: {'继续' if auto_play else '暂停'}")
            elif key == ord('y'):  # y键标记
                mark_image(directory, filename, marked_images)
            elif key == ord('n'):  # n键取消标记
                unmark_image(directory, filename, marked_images)
            elif key == 81 or key == 2:  # 左方向键
                if current_index > 0:
                    current_index -= 1
                    auto_play = False
                    continue
            elif key == 83 or key == 3:  # 右方向键
                if current_index < len(image_files) - 1:
                    current_index += 1
                    auto_play = False
                    continue
            
            # 自动播放逻辑
            if auto_play and time.time() - last_change_time >= display_time:
                current_index += 1
                last_change_time = time.time()
        
        print("播放完成!")
        
    except KeyboardInterrupt:
        print("\n用户中断播放")
    finally:
        cv2.destroyAllWindows()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='YOLOv8图片查看标记器')
    parser.add_argument('directory', nargs='?', default='/Users/<USER>/Desktop/100NCZ_9',
                       help='图片目录路径 (默认: /Users/<USER>/Desktop/100NCZ_9)')
    parser.add_argument('--model', 
                       default='/Users/<USER>/Desktop/YOLOv8-11/YOLOv8/best.pt',
                       help='YOLOv8模型路径')
    
    args = parser.parse_args()
    
    # 检查目录是否存在
    if not os.path.isdir(args.directory):
        print(f"错误: 目录不存在 - {args.directory}")
        return 1
    
    # 检查模型文件
    if args.model and not os.path.exists(args.model):
        print(f"警告: 模型文件不存在 - {args.model}")
        print("将在不使用YOLOv8检测的情况下运行")
        args.model = None
    
    # 开始播放
    view_images(args.directory, args.model)
    return 0

if __name__ == '__main__':
    exit(main())
