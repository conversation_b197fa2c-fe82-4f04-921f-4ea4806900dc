#!/usr/bin/env python3
"""
NEF对焦点标记工具

功能：
1. 自动读取尼康NEF(RAW)文件中的对焦点位置信息
2. 在同名JPG文件上标记对焦点区域
3. 标记为5像素宽的洋红色矩形框
4. 自动处理不同尺寸图像的坐标转换

使用方式：
python3 mark_focus_point.py <NEF文件路径>

输出：
生成原文件名_marked.JPG的标记后图像

依赖：
- exiftool (需预先安装)
- Python PIL/Pillow库
"""

import os
import subprocess
import time
import logging
from PIL import Image, ImageDraw

# 设置日志
script_name = os.path.splitext(os.path.basename(__file__))[0]
log_file = os.path.abspath(f"{script_name}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(script_name)

start_time = time.time()

def get_focus_info(nef_path):
    """使用exiftool获取对焦点信息"""
    cmd = f'exiftool -AFAreaXPosition -AFAreaYPosition -AFAreaWidth -AFAreaHeight {nef_path}'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    # 调试输出
    print("Exiftool原始输出:")
    print(result.stdout)
    
    # 解析输出
    values = []
    for line in result.stdout.splitlines():
        if ":" in line:
            value = line.split(":")[1].strip()
            try:
                values.append(int(value))
            except ValueError:
                continue
    
    if len(values) != 4:
        raise ValueError(f"无法解析对焦点信息，获取到{len(values)}个值，需要4个")
    
    return tuple(values)

def mark_focus_point(jpg_path, x, y, w, h, color=(255, 0, 255), width=5):
    """在JPG图像上标记对焦点区域"""
    with Image.open(jpg_path) as img:
        draw = ImageDraw.Draw(img)
        
        # 计算缩放比例(假设JPG是从NEF全尺寸导出)
        scale_x = img.width / 8256
        scale_y = img.height / 5504
        
        # 计算JPG上的对焦点坐标和尺寸
        jpg_x = int(x * scale_x)
        jpg_y = int(y * scale_y)
        jpg_w = int(w * scale_x)
        jpg_h = int(h * scale_y)
        
        # 计算对焦点区域边界
        left = jpg_x - jpg_w//2
        right = jpg_x + jpg_w//2
        top = jpg_y - jpg_h//2
        bottom = jpg_y + jpg_h//2
        
        # 根据AF区域大小动态调整角标长度
        corner_length = 20 if w < 160 and h < 160 else 30
        
        # 绘制四个角的标记
        # 左上角
        draw.line([(left, top), (left + corner_length, top)], fill=color, width=width)
        draw.line([(left, top), (left, top + corner_length)], fill=color, width=width)
        # 右上角
        draw.line([(right, top), (right - corner_length, top)], fill=color, width=width)
        draw.line([(right, top), (right, top + corner_length)], fill=color, width=width)
        # 左下角
        draw.line([(left, bottom), (left + corner_length, bottom)], fill=color, width=width)
        draw.line([(left, bottom), (left, bottom - corner_length)], fill=color, width=width)
        # 右下角
        draw.line([(right, bottom), (right - corner_length, bottom)], fill=color, width=width)
        draw.line([(right, bottom), (right, bottom - corner_length)], fill=color, width=width)
        
        # 直接覆盖原JPG文件
        img.save(jpg_path)
        return jpg_path

def process_directory(directory):
    """处理目录下所有NEF文件"""
    for filename in os.listdir(directory):
        if filename.upper().endswith('.NEF'):
            nef_path = os.path.join(directory, filename)
            jpg_path = os.path.splitext(nef_path)[0] + '.JPG'
            
            if not os.path.exists(jpg_path):
                print(f"警告: 跳过 {filename}, 找不到对应的JPG文件")
                continue
                
            try:
                x, y, w, h = get_focus_info(nef_path)
                output_path = mark_focus_point(jpg_path, x, y, w, h)
                print(f"成功处理: {filename} -> {os.path.basename(output_path)}")
            except Exception as e:
                logger.error(f"处理 {filename} 时出错: {e}")
                print(f"处理 {filename} 时出错: {e}")
    
    elapsed_time = time.time() - start_time
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)
    print(f"\n处理完成 - 总耗时: {hours:02d}:{minutes:02d}")

if __name__ == '__main__':
    import sys
    if len(sys.argv) != 2:
        print(f"Usage: {sys.argv[0]} <NEF文件路径或目录>")
        sys.exit(1)
        
    path = sys.argv[1]
    if os.path.isdir(path):
        process_directory(path)
    else:
        # 处理单个文件
        jpg_path = os.path.splitext(path)[0] + '.JPG'
        if not os.path.exists(jpg_path):
            print(f"错误: 找不到对应的JPG文件 {jpg_path}")
            sys.exit(1)
            
        try:
            x, y, w, h = get_focus_info(path)
            output_path = mark_focus_point(jpg_path, x, y, w, h)
            print(f"成功: 已标记对焦点 {output_path}")
        except Exception as e:
            print(f"错误: {e}")