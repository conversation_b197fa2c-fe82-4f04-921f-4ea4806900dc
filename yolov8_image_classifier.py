#!/usr/bin/env python3
"""
YOLOv8图像分类器

功能：
1. 使用YOLOv8模型对图像进行分类
2. 根据分类结果自动整理文件：
   - 保留bird_eye,bird_clear,bird_flying在原目录
   - 将bird_blurry,no_bird,bird_back移动到no_bird子目录
3. 支持常见图片格式(.jpg, .jpeg, .png, .nef)
"""

import os
import shutil
import time
import logging
from ultralytics import YOLO

# 设置日志
script_name = os.path.splitext(os.path.basename(__file__))[0]
log_file = os.path.abspath(f"{script_name}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(script_name)

start_time = time.time()

def classify_images(source_dir, model_path='/Users/<USER>/Desktop/bird_feather_project/src/runs/detect/bird_feather_detection6/weights/best.pt'):
    """执行图像分类和整理"""
    # 加载分类模型
    model = YOLO(model_path)
    
    # 创建目标目录
    no_bird_dir = os.path.join(source_dir, 'no_bird')
    os.makedirs(no_bird_dir, exist_ok=True)
    
    # 支持的图片格式 (处理JPG和NEF对)
    img_exts = ('.jpg', '.jpeg', '.nef')
    
    # 定义分类规则
    KEEP_CLASSES = ['bird_eye', 'bird_clear', 'bird_flying']
    MOVE_CLASSES = ['bird_blurry', 'no_bird', 'bird_back']
    
    # 收集所有文件对
    file_pairs = {}
    for filename in os.listdir(source_dir):
        filepath = os.path.join(source_dir, filename)
        if not os.path.isfile(filepath):
            continue
            
        name, ext = os.path.splitext(filename)
        ext = ext.lower()
        if ext in ('.jpg', '.jpeg', '.nef'):
            if name not in file_pairs:
                file_pairs[name] = {}
            file_pairs[name][ext] = filepath
    
    # 处理每对文件
    for base_name, files in file_pairs.items():
        # 必须有JPG文件才能处理
        if '.jpg' not in files and '.jpeg' not in files:
            continue
            
        jpg_path = files.get('.jpg') or files.get('.jpeg')
            
        filename = os.path.basename(jpg_path)
        nef_path = files.get('.nef')
            
        # 执行检测预测(使用JPG文件)
        results = model(jpg_path)
        if len(results) == 0:
            pred_class = 'no_bird'
        else:
            # 获取检测到的类别
            detected_classes = set()
            for r in results:
                for c in r.boxes.cls:
                    detected_classes.add(r.names[int(c)])
            
            # 根据检测结果确定分类
            if 'bird_eye' in detected_classes or 'bird_clear' in detected_classes or 'bird_flying' in detected_classes:
                pred_class = 'bird_clear'  # 代表保留的类别
            else:
                pred_class = 'no_bird'  # 代表需要移动的类别
        
        # 根据分类结果处理文件对
        if pred_class in MOVE_CLASSES:
            # 移动JPG文件
            shutil.move(jpg_path, os.path.join(no_bird_dir, filename))
            # 移动对应的NEF文件(如果存在)
            if nef_path:
                nef_filename = os.path.basename(nef_path)
                shutil.move(nef_path, os.path.join(no_bird_dir, nef_filename))
            logger.info(f"Moved pair: {base_name}.* -> no_bird/")
            print(f"Moved pair: {base_name}.* -> no_bird/")
        elif pred_class in KEEP_CLASSES:
            logger.info(f"Kept pair: {base_name}.* (class: {pred_class})")
            print(f"Kept pair: {base_name}.* (class: {pred_class})")
        else:
            logger.warning(f"Unknown class: {base_name}.* (class: {pred_class}), kept in place")
            print(f"Unknown class: {base_name}.* (class: {pred_class}), kept in place")

if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser(description='YOLOv8图像分类器')
    parser.add_argument('source_dir', help='源图片目录路径')
    parser.add_argument('--model', default='/Users/<USER>/Desktop/bird_feather_project/src/runs/detect/bird_feather_detection6/weights/best.pt',
                      help='YOLOv8分类模型路径(默认: /Users/<USER>/Desktop/bird_feather_project/src/runs/detect/bird_feather_detection6/weights/best.pt)')
    args = parser.parse_args()

    if not os.path.isdir(args.source_dir):
        print(f"错误: 目录不存在 - {args.source_dir}")
        exit(1)

    classify_images(args.source_dir, args.model)