#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# -*- coding: utf-8 -*-
"""
JPG拍摄参数叠加工具 v1.3

功能：
1. 读取NEF文件中的拍摄参数(光圈、快门、ISO、焦距、曝光补偿、对焦距离)
2. 在对应的JPG文件左下角添加半透明参数信息
3. 保留原始EXIF信息
4. 支持批量处理目录下所有NEF文件
5. 自动适应不同尺寸的JPG文件(8256x5504和6192x4128)
6. 参数文本和背景宽度自动调整
7. 文本位置：左侧300px/底部600px（全尺寸图片）
8. 字体大小：150px（全尺寸图片）

使用方法：
python3 add_shooting_params.py [图片目录路径]

示例：
python3 add_shooting_params.py [图片目录路径]

示例：
python3 add_shooting_params.py /Users/<USER>/Desktop/400NCZ_9

参数位置和大小设置：
- 全尺寸图片(8256x5504)：
  - 文本左侧距离：300px
  - 文本底部距离：600px
  - 字体大小：150px
- 其他尺寸图片自动按比例缩放
"""

import subprocess
from PIL import Image, ImageDraw
import os
import sys
import time
import logging
import re

# 设置日志
script_name = os.path.splitext(os.path.basename(__file__))[0]
log_file = os.path.join(os.getcwd(), f"{script_name}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(script_name)
import time

start_time = time.time()

def get_shooting_params(nef_path):
    """获取拍摄参数(光圈、快门、ISO等)"""
    try:
        result = subprocess.run(
            ['exiftool', '-G1', '-a', '-u', '-s',
             '-FNumber', '-ExposureTime', '-ISO', '-FocalLength',
             '-ExposureCompensation', '-FocusDistance', nef_path],
            capture_output=True, check=True
        )
        output = result.stdout.decode('utf-8', errors='ignore')
        params = {}
        
        for line in output.splitlines():
            if 'FNumber' in line and 'Nikon' not in line:  # 避免重复读取
                params['aperture'] = line.split(':')[-1].strip().replace('f/','')
            elif 'ExposureTime' in line and 'Nikon' not in line:
                shutter = line.split(':')[-1].strip()
                if '/' in shutter:
                    params['shutter'] = shutter.replace('1/','') + '"'
                else:
                    params['shutter'] = shutter + 's'
            elif 'ISO' in line and 'Nikon' not in line:
                params['iso'] = line.split(':')[-1].strip()
            elif 'FocalLength' in line and 'Nikon' not in line:
                params['focal'] = line.split(':')[-1].strip().replace(' mm','')
            elif 'ExposureCompensation' in line:
                params['ev'] = line.split(':')[-1].strip()
            elif 'FocusDistance' in line:
                params['distance'] = line.split(':')[-1].strip()
        
        # 格式化参数显示
        formatted = []
        if 'aperture' in params:
            formatted.append(f"ƒ/{params['aperture']}")
        if 'shutter' in params:
            formatted.append(f"{params['shutter']}")
        if 'iso' in params:
            formatted.append(f"ISO {params['iso']}")
        if 'focal' in params:
            formatted.append(f"{params['focal']}mm")
        if 'ev' in params:
            formatted.append(f"EV {params['ev']}")
        if 'distance' in params:
            formatted.append(f"{params['distance']}m")
            
        return '  '.join(formatted)
    except Exception as e:
        print(f"获取拍摄参数失败: {e}")
        return None

def add_params_to_jpg(jpg_path, params):
    """在JPG左下角添加拍摄参数"""
    try:
        img = Image.open(jpg_path)
        exif = img.info.get('exif', b'')
        draw = ImageDraw.Draw(img)
        
        # 根据JPG尺寸计算参数位置
        img_width, img_height = img.size
        scale = img_width / 8256 if img_width != 8256 else 1
        
        # 计算文本尺寸
        from PIL import ImageFont
        font_size = int(150 * scale)  # 基础字体大小从100px调整为150px
        font = ImageFont.load_default() if font_size < 5 else ImageFont.truetype("Arial", font_size)
        
        # 计算文本宽度
        text = params  # 直接使用格式化后的字符串
        # 使用ImageDraw.textlength替代已弃用的font.getsize()
        # 获取文本的精确尺寸
        text_bbox = draw.textbbox((0, 0), text, font=font)
        text_width = text_bbox[2] - text_bbox[0]  # 实际宽度
        text_height = text_bbox[3] - text_bbox[1]  # 实际高度
        print(f"文本尺寸: 宽度={text_width}px, 高度={text_height}px")
        
        # 创建半透明黑色背景
        bg_width = int(3500 * scale)
        bg_height = int(150 * scale)
        bg = Image.new('RGBA', (bg_width, bg_height), (0, 0, 0, 128))  # 半透明黑色
        img.paste(bg, (int(270 * scale), img_height - int(590 * scale)), bg)  # 固定位置
        
        # 添加参数文本
        text_x = int(300 * scale)  # 全尺寸图片左侧距离300px
        text_y = img_height - int(600 * scale)  # 全尺寸图片基线距离底部600px
        draw.text((text_x, text_y), text, fill="white", font=font)
        
        # 保存文件
        img.save(jpg_path, quality=95, exif=exif)
        print(f"成功添加拍摄参数: {jpg_path}")
        return True
    except Exception as e:
        print(f"添加拍摄参数失败: {e}")
        return False

def get_focus_info(nef_path):
    """使用exiftool获取对焦点信息"""
    cmd = f'exiftool -AFAreaXPosition -AFAreaYPosition -AFAreaWidth -AFAreaHeight {nef_path}'
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    # 解析输出
    values = []
    for line in result.stdout.splitlines():
        if ":" in line:
            value = line.split(":")[1].strip()
            try:
                values.append(int(value))
            except ValueError:
                continue
    
    if len(values) != 4:
        raise ValueError(f"无法解析对焦点信息，获取到{len(values)}个值，需要4个")
    
    return tuple(values)

def mark_focus_point(jpg_path, x, y, w, h, color=(255, 0, 255), width=5):
    """在JPG图像上标记对焦点区域"""
    img = Image.open(jpg_path)
    draw = ImageDraw.Draw(img)
    
    # 计算缩放比例(假设JPG是从NEF全尺寸导出)
    scale_x = img.width / 8256
    scale_y = img.height / 5504
    
    # 计算JPG上的对焦点坐标和尺寸
    jpg_x = int(x * scale_x)
    jpg_y = int(y * scale_y)
    jpg_w = int(w * scale_x)
    jpg_h = int(h * scale_y)
    
    # 计算对焦点区域边界
    left = jpg_x - jpg_w//2
    right = jpg_x + jpg_w//2
    top = jpg_y - jpg_h//2
    bottom = jpg_y + jpg_h//2
    
    # 根据AF区域大小动态调整角标长度
    corner_length = 20 if w < 160 and h < 160 else 30
    
    # 绘制四个角的标记
    # 左上角
    draw.line([(left, top), (left + corner_length, top)], fill=color, width=width)
    draw.line([(left, top), (left, top + corner_length)], fill=color, width=width)
    # 右上角
    draw.line([(right, top), (right - corner_length, top)], fill=color, width=width)
    draw.line([(right, top), (right, top + corner_length)], fill=color, width=width)
    # 左下角
    draw.line([(left, bottom), (left + corner_length, bottom)], fill=color, width=width)
    draw.line([(left, bottom), (left, bottom - corner_length)], fill=color, width=width)
    # 右下角
    draw.line([(right, bottom), (right - corner_length, bottom)], fill=color, width=width)
    draw.line([(right, bottom), (right, bottom - corner_length)], fill=color, width=width)
    
    # 保存文件
    img.save(jpg_path)
    return jpg_path

def process_directory(directory):
    """处理目录下所有NEF文件"""
    nef_files = [f for f in os.listdir(directory) if f.lower().endswith('.nef')]
    
    if not nef_files:
        print(f"目录 {directory} 中没有NEF文件")
        return
    
    success = 0
    for nef_file in nef_files:
        nef_path = os.path.join(directory, nef_file)
        jpg_path = os.path.splitext(nef_path)[0] + '.jpg'
        
        if not os.path.exists(jpg_path):
            jpg_path = os.path.splitext(nef_path)[0] + '.JPG'
            if not os.path.exists(jpg_path):
                print(f"未找到 {nef_file} 对应的JPG文件")
                continue
        
        params = get_shooting_params(nef_path)
        if params:
            if add_params_to_jpg(jpg_path, params):
                success += 1
    
    elapsed_time = time.time() - start_time
    hours = int(elapsed_time // 3600)
    minutes = int((elapsed_time % 3600) // 60)
    logger.info(f"处理完成！成功添加 {success}/{len(nef_files)} 个文件的拍摄参数")
    print(f"\n处理完成！成功添加 {success}/{len(nef_files)} 个文件的拍摄参数")
    print(f"总耗时: {hours:02d}:{minutes:02d}")

if __name__ == '__main__':
    if len(sys.argv) != 2:
        print("用法: python add_shooting_params.py <目录路径>")
        sys.exit(1)
    
    process_directory(sys.argv[1])